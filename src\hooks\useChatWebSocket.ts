import { useState, useEffect, useCallback, useMemo } from 'react';
import useWebSocket from './useWebSocket';
import { useWebSocketData } from './useWebSocketData';
import {
  Conversation,
  Message,
  ConversationsResponse,
  ConversationResponse,
  MessagesResponse,
  GetConversationsParams,
  GetMessagesParams,
  CreateConversationRequest,
} from '@/store/api/chatApiSlice';

interface UseChatWebSocketOptions {
  userId: string;
  userType: 'nurse' | 'patient';
  userName: string;
  enabled?: boolean;
  onMessage?: (message: any) => void;
}

export const useChatWebSocket = ({
  userId,
  userType,
  userName,
  enabled = true,
  onMessage,
}: UseChatWebSocketOptions) => {
  const baseWsUrl = import.meta.env.VITE_CHAT_WS_URL || 'wss://chatapi.nurserv.com';
  const wsUrl = baseWsUrl.endsWith('/ws') ? baseWsUrl : `${baseWsUrl.replace(/\/+$/, '')}/ws`;
  const token = localStorage.getItem('idToken') || '';

  // State for data management
  const [state, setState] = useState({
    conversations: { data: [], loading: false, error: null, pagination: undefined },
    conversationDetails: {},
    messages: {},
  });

  const pendingRequestsRef = useRef<Record<string, any>>({});

  // Combined message handler
  const combinedOnMessage = useCallback((message: any) => {
    // Handle data responses
    if (message.requestId && pendingRequestsRef.current[message.requestId]) {
      const request = pendingRequestsRef.current[message.requestId];

      if (message.success === false || message.error) {
        request.reject(new Error(message.error || 'Request failed'));
      } else {
        request.resolve(message.data);
      }

      delete pendingRequestsRef.current[message.requestId];
    }

    // Handle real-time updates
    switch (message.type) {
      case 'CONVERSATIONS_RESPONSE':
        setState(prev => ({
          ...prev,
          conversations: {
            data: message.data?.conversations || [],
            loading: false,
            error: message.error || null,
            pagination: message.data?.pagination,
          },
        }));
        break;

      case 'CONVERSATION_RESPONSE':
        if (message.data?.conversation) {
          setState(prev => ({
            ...prev,
            conversationDetails: {
              ...prev.conversationDetails,
              [message.data.conversation.id]: {
                data: message.data.conversation,
                loading: false,
                error: null,
              },
            },
          }));
        }
        break;

      case 'MESSAGES_RESPONSE':
        if (message.conversationId) {
          setState(prev => ({
            ...prev,
            messages: {
              ...prev.messages,
              [message.conversationId]: {
                data: message.data?.messages || [],
                loading: false,
                error: message.error || null,
                pagination: message.data?.pagination,
              },
            },
          }));
        }
        break;

      case 'TEXT_MESSAGE':
        // Handle new messages
        if (message.conversationId && message.senderId) {
          const newMessage = {
            id: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            conversationId: message.conversationId,
            senderId: message.senderId,
            senderType: message.senderType || 'patient',
            senderName: message.senderName || 'Unknown',
            content: message.content || '',
            type: 'text',
            status: 'delivered',
            timestamp: message.timestamp || new Date().toISOString(),
            metadata: message.metadata,
          };

          setState(prev => ({
            ...prev,
            messages: {
              ...prev.messages,
              [message.conversationId]: {
                ...prev.messages[message.conversationId],
                data: [
                  ...(prev.messages[message.conversationId]?.data || []),
                  newMessage,
                ],
              },
            },
          }));
        }
        break;
    }

    // Call the original onMessage handler if provided
    if (onMessage) {
      onMessage(message);
    }
  }, [onMessage]);

  const {
    status: wsStatus,
    sendTextMessage,
    sendTypingIndicator,
    sendReadReceipt,
    joinConversation,
    leaveConversation,
    typingUsers,
    activeConversations,
    requestConversations,
    requestConversation,
    requestMessages,
    createConversationWS,
    markMessagesAsReadWS,
    updateConversationStatusWS,
  } = useWebSocket({
    url: wsUrl,
    token,
    userId,
    userType,
    userName,
    enabled,
    onMessage: combinedOnMessage,
  });

  // API-like functions
  const getConversations = useCallback(async (params?: any) => {
    setState(prev => ({
      ...prev,
      conversations: { ...prev.conversations, loading: true, error: null },
    }));

    const { requestId, success } = requestConversations(params);

    if (!success) {
      setState(prev => ({
        ...prev,
        conversations: { ...prev.conversations, loading: false, error: 'Failed to send request' },
      }));
      throw new Error('Failed to send request');
    }

    return new Promise((resolve, reject) => {
      pendingRequestsRef.current[requestId] = { resolve, reject };

      setTimeout(() => {
        if (pendingRequestsRef.current[requestId]) {
          delete pendingRequestsRef.current[requestId];
          reject(new Error('Request timeout'));
        }
      }, 30000);
    });
  }, [requestConversations]);

  const getConversation = useCallback(async (conversationId: string) => {
    setState(prev => ({
      ...prev,
      conversationDetails: {
        ...prev.conversationDetails,
        [conversationId]: { data: null, loading: true, error: null },
      },
    }));

    const { requestId, success } = requestConversation(conversationId);

    if (!success) {
      setState(prev => ({
        ...prev,
        conversationDetails: {
          ...prev.conversationDetails,
          [conversationId]: { data: null, loading: false, error: 'Failed to send request' },
        },
      }));
      throw new Error('Failed to send request');
    }

    return new Promise((resolve, reject) => {
      pendingRequestsRef.current[requestId] = { resolve, reject };

      setTimeout(() => {
        if (pendingRequestsRef.current[requestId]) {
          delete pendingRequestsRef.current[requestId];
          reject(new Error('Request timeout'));
        }
      }, 30000);
    });
  }, [requestConversation]);

  const getMessages = useCallback(async (params: any) => {
    const { conversationId } = params;

    setState(prev => ({
      ...prev,
      messages: {
        ...prev.messages,
        [conversationId]: {
          data: prev.messages[conversationId]?.data || [],
          loading: true,
          error: null
        },
      },
    }));

    const { requestId, success } = requestMessages(conversationId, params);

    if (!success) {
      setState(prev => ({
        ...prev,
        messages: {
          ...prev.messages,
          [conversationId]: {
            data: prev.messages[conversationId]?.data || [],
            loading: false,
            error: 'Failed to send request'
          },
        },
      }));
      throw new Error('Failed to send request');
    }

    return new Promise((resolve, reject) => {
      pendingRequestsRef.current[requestId] = { resolve, reject };

      setTimeout(() => {
        if (pendingRequestsRef.current[requestId]) {
          delete pendingRequestsRef.current[requestId];
          reject(new Error('Request timeout'));
        }
      }, 30000);
    });
  }, [requestMessages]);

  const createConversation = useCallback(async (data: any) => {
    const { requestId, success } = createConversationWS(data.nurseId, data.nurseName, data.initialMessage);

    if (!success) {
      throw new Error('Failed to send request');
    }

    return new Promise((resolve, reject) => {
      pendingRequestsRef.current[requestId] = { resolve, reject };

      setTimeout(() => {
        if (pendingRequestsRef.current[requestId]) {
          delete pendingRequestsRef.current[requestId];
          reject(new Error('Request timeout'));
        }
      }, 30000);
    });
  }, [createConversationWS]);

  const markMessagesAsRead = useCallback(async (conversationId: string) => {
    const { requestId, success } = markMessagesAsReadWS(conversationId);

    if (!success) {
      throw new Error('Failed to send request');
    }

    return new Promise((resolve, reject) => {
      pendingRequestsRef.current[requestId] = { resolve, reject };

      setTimeout(() => {
        if (pendingRequestsRef.current[requestId]) {
          delete pendingRequestsRef.current[requestId];
          reject(new Error('Request timeout'));
        }
      }, 30000);
    });
  }, [markMessagesAsReadWS]);

  const updateConversationStatus = useCallback(async (data: any) => {
    const { requestId, success } = updateConversationStatusWS(data.conversationId, data.status);

    if (!success) {
      throw new Error('Failed to send request');
    }

    return new Promise((resolve, reject) => {
      pendingRequestsRef.current[requestId] = { resolve, reject };

      setTimeout(() => {
        if (pendingRequestsRef.current[requestId]) {
          delete pendingRequestsRef.current[requestId];
          reject(new Error('Request timeout'));
        }
      }, 30000);
    });
  }, [updateConversationStatusWS]);

  return {
    // WebSocket status and connection functions
    wsStatus,
    sendTextMessage,
    sendTypingIndicator,
    sendReadReceipt,
    joinConversation,
    leaveConversation,
    typingUsers,
    activeConversations,

    // Data state
    state,

    // API-like functions
    getConversations,
    getConversation,
    getMessages,
    createConversation,
    markMessagesAsRead,
    updateConversationStatus,
  };
};

// Hook that mimics useGetConversationsQuery
export const useGetConversationsQuery = (
  params?: GetConversationsParams | void,
  options?: { skip?: boolean; refetchOnFocus?: boolean; refetchOnReconnect?: boolean }
) => {
  const userId = localStorage.getItem('userId') || '';
  const userGivenName = localStorage.getItem('userGivenName') || 'Patient';
  const userType = 'patient';

  const { state, getConversations } = useChatWebSocket({
    userId,
    userType,
    userName: userGivenName,
    enabled: !options?.skip,
  });

  const [hasInitialFetch, setHasInitialFetch] = useState(false);

  // Trigger initial fetch
  useEffect(() => {
    if (!options?.skip && !hasInitialFetch && !state.conversations.loading) {
      getConversations(params || {}).catch(console.error);
      setHasInitialFetch(true);
    }
  }, [options?.skip, hasInitialFetch, state.conversations.loading, getConversations, params]);

  const refetch = useCallback(() => {
    return getConversations(params || {});
  }, [getConversations, params]);

  return {
    data: state.conversations.data.length > 0 ? {
      success: true,
      conversations: state.conversations.data,
      pagination: state.conversations.pagination,
      data: {
        conversations: state.conversations.data,
        pagination: state.conversations.pagination || { page: 1, limit: 50, total: 0, totalPages: 0 },
      },
    } : undefined,
    isLoading: state.conversations.loading,
    error: state.conversations.error ? { message: state.conversations.error } : null,
    refetch,
  };
};

// Hook that mimics useGetConversationQuery
export const useGetConversationQuery = (
  conversationId: string,
  options?: { skip?: boolean }
) => {
  const userId = localStorage.getItem('userId') || '';
  const userGivenName = localStorage.getItem('userGivenName') || 'Patient';
  const userType = 'patient';

  const { state, getConversation } = useChatWebSocket({
    userId,
    userType,
    userName: userGivenName,
    enabled: !options?.skip,
  });

  const [hasInitialFetch, setHasInitialFetch] = useState(false);
  const conversationState = state.conversationDetails[conversationId];

  // Trigger initial fetch
  useEffect(() => {
    if (!options?.skip && conversationId && !hasInitialFetch && !conversationState?.loading) {
      getConversation(conversationId).catch(console.error);
      setHasInitialFetch(true);
    }
  }, [options?.skip, conversationId, hasInitialFetch, conversationState?.loading, getConversation]);

  const refetch = useCallback(() => {
    return getConversation(conversationId);
  }, [getConversation, conversationId]);

  return {
    data: conversationState?.data ? {
      success: true,
      conversation: conversationState.data,
      data: { conversation: conversationState.data },
    } : undefined,
    isLoading: conversationState?.loading || false,
    error: conversationState?.error ? { message: conversationState.error } : null,
    refetch,
  };
};

// Hook that mimics useGetMessagesQuery
export const useGetMessagesQuery = (
  params: GetMessagesParams,
  options?: { skip?: boolean; pollingInterval?: number }
) => {
  const userId = localStorage.getItem('userId') || '';
  const userGivenName = localStorage.getItem('userGivenName') || 'Patient';
  const userType = 'patient';

  const { state, getMessages } = useChatWebSocket({
    userId,
    userType,
    userName: userGivenName,
    enabled: !options?.skip,
  });

  const [hasInitialFetch, setHasInitialFetch] = useState(false);
  const messagesState = state.messages[params.conversationId];

  // Trigger initial fetch
  useEffect(() => {
    if (!options?.skip && params.conversationId && !hasInitialFetch && !messagesState?.loading) {
      getMessages(params).catch(console.error);
      setHasInitialFetch(true);
    }
  }, [options?.skip, params.conversationId, hasInitialFetch, messagesState?.loading, getMessages, params]);

  const refetch = useCallback(() => {
    return getMessages(params);
  }, [getMessages, params]);

  return {
    data: messagesState?.data ? {
      success: true,
      messages: messagesState.data,
      pagination: messagesState.pagination,
      data: {
        messages: messagesState.data,
        pagination: messagesState.pagination || { page: 1, limit: 50, total: 0, totalPages: 0 },
      },
    } : undefined,
    isLoading: messagesState?.loading || false,
    error: messagesState?.error ? { message: messagesState.error } : null,
    refetch,
  };
};

// Hook that mimics useCreateConversationMutation
export const useCreateConversationMutation = () => {
  const userId = localStorage.getItem('userId') || '';
  const userGivenName = localStorage.getItem('userGivenName') || 'Patient';
  const userType = 'patient';

  const { createConversation } = useChatWebSocket({
    userId,
    userType,
    userName: userGivenName,
  });

  const [isLoading, setIsLoading] = useState(false);

  const mutate = useCallback(async (data: CreateConversationRequest) => {
    setIsLoading(true);
    try {
      const result = await createConversation(data);
      setIsLoading(false);
      return result;
    } catch (error) {
      setIsLoading(false);
      throw error;
    }
  }, [createConversation]);

  return [mutate, { isLoading }];
};

// Hook that mimics useMarkMessagesAsReadMutation
export const useMarkMessagesAsReadMutation = () => {
  const userId = localStorage.getItem('userId') || '';
  const userGivenName = localStorage.getItem('userGivenName') || 'Patient';
  const userType = 'patient';

  const { markMessagesAsRead } = useChatWebSocket({
    userId,
    userType,
    userName: userGivenName,
  });

  const [isLoading, setIsLoading] = useState(false);

  const mutate = useCallback(async (conversationId: string) => {
    setIsLoading(true);
    try {
      const result = await markMessagesAsRead(conversationId);
      setIsLoading(false);
      return result;
    } catch (error) {
      setIsLoading(false);
      throw error;
    }
  }, [markMessagesAsRead]);

  return [mutate, { isLoading }];
};

// Hook that mimics useUpdateConversationStatusMutation
export const useUpdateConversationStatusMutation = () => {
  const userId = localStorage.getItem('userId') || '';
  const userGivenName = localStorage.getItem('userGivenName') || 'Patient';
  const userType = 'patient';

  const { updateConversationStatus } = useChatWebSocket({
    userId,
    userType,
    userName: userGivenName,
  });

  const [isLoading, setIsLoading] = useState(false);

  const mutate = useCallback(async (data: { conversationId: string; status: 'active' | 'inactive' | 'archived' }) => {
    setIsLoading(true);
    try {
      const result = await updateConversationStatus(data);
      setIsLoading(false);
      return result;
    } catch (error) {
      setIsLoading(false);
      throw error;
    }
  }, [updateConversationStatus]);

  return [mutate, { isLoading }];
};

// Hook that mimics useSendMessageMutation
export const useSendMessageMutation = () => {
  const userId = localStorage.getItem('userId') || '';
  const userGivenName = localStorage.getItem('userGivenName') || 'Patient';
  const userType = 'patient';

  const { sendTextMessage } = useChatWebSocket({
    userId,
    userType,
    userName: userGivenName,
  });

  const [isLoading, setIsLoading] = useState(false);

  const mutate = useCallback(async (data: { conversationId: string; content: string; type?: string; metadata?: any }) => {
    setIsLoading(true);
    try {
      // Use WebSocket to send the message
      const success = sendTextMessage(data.conversationId, data.content, data.metadata);
      setIsLoading(false);

      if (success) {
        // Return a structure similar to the REST API response
        return {
          success: true,
          data: {
            message: {
              id: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
              conversationId: data.conversationId,
              senderId: userId,
              senderType: userType,
              senderName: userGivenName,
              content: data.content,
              type: data.type || 'text',
              status: 'sent',
              timestamp: new Date().toISOString(),
              metadata: data.metadata,
            },
          },
        };
      } else {
        throw new Error('Failed to send message via WebSocket');
      }
    } catch (error) {
      setIsLoading(false);
      throw error;
    }
  }, [sendTextMessage, userId, userType, userGivenName]);

  return [mutate, { isLoading }];
};

// Hook that mimics useGetUnreadCountQuery
export const useGetUnreadCountQuery = (
  _params?: void,
  options?: { skip?: boolean; pollingInterval?: number }
) => {
  const userId = localStorage.getItem('userId') || '';
  const userGivenName = localStorage.getItem('userGivenName') || 'Patient';
  const userType = 'patient';

  const { state, getConversations } = useChatWebSocket({
    userId,
    userType,
    userName: userGivenName,
    enabled: !options?.skip,
  });

  const [hasInitialFetch, setHasInitialFetch] = useState(false);

  // Trigger initial fetch to get conversations for unread count
  useEffect(() => {
    if (!options?.skip && !hasInitialFetch && !state.conversations.loading) {
      getConversations({}).catch(console.error);
      setHasInitialFetch(true);
    }
  }, [options?.skip, hasInitialFetch, state.conversations.loading, getConversations]);

  const refetch = useCallback(() => {
    return getConversations({});
  }, [getConversations]);

  // Calculate unread count from conversations
  const unreadCount = useMemo(() => {
    return state.conversations.data.reduce(
      (total, conversation) => total + (conversation.unreadCount || 0),
      0
    );
  }, [state.conversations.data]);

  return {
    data: { unreadCount },
    isLoading: state.conversations.loading,
    error: state.conversations.error ? { message: state.conversations.error } : null,
    refetch,
  };
};
