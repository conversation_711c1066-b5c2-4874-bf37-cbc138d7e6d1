import { useState, useEffect, useCallback, useRef } from 'react';
import { WebSocketMessage } from './useWebSocket';
import {
  Conversation,
  Message,
  ConversationsResponse,
  ConversationResponse,
  MessagesResponse,
  SendMessageResponse,
} from '@/store/api/chatApiSlice';

interface WebSocketDataState {
  conversations: {
    data: Conversation[];
    loading: boolean;
    error: string | null;
    pagination?: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  };
  conversationDetails: Record<string, {
    data: Conversation | null;
    loading: boolean;
    error: string | null;
  }>;
  messages: Record<string, {
    data: Message[];
    loading: boolean;
    error: string | null;
    pagination?: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  }>;
}

interface UseWebSocketDataOptions {
  onMessage?: (message: WebSocketMessage) => void;
  requestConversations: (params?: any) => { requestId: string; success: boolean };
  requestConversation: (conversationId: string) => { requestId: string; success: boolean };
  requestMessages: (conversationId: string, params?: any) => { requestId: string; success: boolean };
  createConversationWS: (nurseId: string, nurseName: string, initialMessage?: string) => { requestId: string; success: boolean };
  markMessagesAsReadWS: (conversationId: string) => { requestId: string; success: boolean };
  updateConversationStatusWS: (conversationId: string, status: 'active' | 'inactive' | 'archived') => { requestId: string; success: boolean };
}

export const useWebSocketData = ({
  onMessage,
  requestConversations,
  requestConversation,
  requestMessages,
  createConversationWS,
  markMessagesAsReadWS,
  updateConversationStatusWS,
}: UseWebSocketDataOptions) => {
  const [state, setState] = useState<WebSocketDataState>({
    conversations: {
      data: [],
      loading: false,
      error: null,
    },
    conversationDetails: {},
    messages: {},
  });

  const pendingRequestsRef = useRef<Record<string, {
    type: string;
    resolve: (value: any) => void;
    reject: (error: any) => void;
    conversationId?: string;
  }>>({});

  // Handle incoming WebSocket messages
  const handleWebSocketMessage = useCallback((message: WebSocketMessage) => {
    const { type, requestId, data, error, success } = message;

    // Call the original onMessage handler if provided
    if (onMessage) {
      onMessage(message);
    }

    // Handle data responses
    if (requestId && pendingRequestsRef.current[requestId]) {
      const request = pendingRequestsRef.current[requestId];
      
      if (success === false || error) {
        request.reject(new Error(error || 'Request failed'));
      } else {
        request.resolve(data);
      }
      
      delete pendingRequestsRef.current[requestId];
      return;
    }

    // Handle real-time updates
    switch (type) {
      case 'CONVERSATIONS_RESPONSE':
        setState(prev => ({
          ...prev,
          conversations: {
            data: data?.conversations || [],
            loading: false,
            error: error || null,
            pagination: data?.pagination,
          },
        }));
        break;

      case 'CONVERSATION_RESPONSE':
        if (data?.conversation) {
          setState(prev => ({
            ...prev,
            conversationDetails: {
              ...prev.conversationDetails,
              [data.conversation.id]: {
                data: data.conversation,
                loading: false,
                error: null,
              },
            },
          }));
        }
        break;

      case 'MESSAGES_RESPONSE':
        if (message.conversationId) {
          setState(prev => ({
            ...prev,
            messages: {
              ...prev.messages,
              [message.conversationId!]: {
                data: data?.messages || [],
                loading: false,
                error: error || null,
                pagination: data?.pagination,
              },
            },
          }));
        }
        break;

      case 'CONVERSATION_CREATED':
        if (data?.conversation) {
          setState(prev => ({
            ...prev,
            conversations: {
              ...prev.conversations,
              data: [data.conversation, ...prev.conversations.data],
            },
            conversationDetails: {
              ...prev.conversationDetails,
              [data.conversation.id]: {
                data: data.conversation,
                loading: false,
                error: null,
              },
            },
          }));
        }
        break;

      case 'TEXT_MESSAGE':
        // Handle new messages
        if (message.conversationId && message.senderId) {
          const newMessage: Message = {
            id: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            conversationId: message.conversationId,
            senderId: message.senderId,
            senderType: message.senderType || 'patient',
            senderName: message.senderName || 'Unknown',
            content: message.content || '',
            type: 'text',
            status: 'delivered',
            timestamp: message.timestamp || new Date().toISOString(),
            metadata: message.metadata,
          };

          setState(prev => ({
            ...prev,
            messages: {
              ...prev.messages,
              [message.conversationId!]: {
                ...prev.messages[message.conversationId!],
                data: [
                  ...(prev.messages[message.conversationId!]?.data || []),
                  newMessage,
                ],
              },
            },
          }));

          // Update conversation last message
          setState(prev => ({
            ...prev,
            conversations: {
              ...prev.conversations,
              data: prev.conversations.data.map(conv =>
                conv.id === message.conversationId
                  ? {
                      ...conv,
                      lastMessage: message.content,
                      lastMessageTime: message.timestamp,
                      updatedAt: message.timestamp || new Date().toISOString(),
                    }
                  : conv
              ),
            },
          }));
        }
        break;

      case 'MESSAGES_MARKED_READ':
        if (message.conversationId) {
          setState(prev => ({
            ...prev,
            messages: {
              ...prev.messages,
              [message.conversationId!]: {
                ...prev.messages[message.conversationId!],
                data: prev.messages[message.conversationId!]?.data?.map(msg => ({
                  ...msg,
                  status: 'read' as const,
                })) || [],
              },
            },
            conversations: {
              ...prev.conversations,
              data: prev.conversations.data.map(conv =>
                conv.id === message.conversationId
                  ? { ...conv, unreadCount: 0 }
                  : conv
              ),
            },
          }));
        }
        break;

      case 'STATUS_UPDATED':
        if (message.conversationId && data?.status) {
          setState(prev => ({
            ...prev,
            conversations: {
              ...prev.conversations,
              data: prev.conversations.data.map(conv =>
                conv.id === message.conversationId
                  ? { ...conv, status: data.status }
                  : conv
              ),
            },
            conversationDetails: {
              ...prev.conversationDetails,
              [message.conversationId!]: prev.conversationDetails[message.conversationId!]
                ? {
                    ...prev.conversationDetails[message.conversationId!],
                    data: prev.conversationDetails[message.conversationId!].data
                      ? { ...prev.conversationDetails[message.conversationId!].data!, status: data.status }
                      : null,
                  }
                : prev.conversationDetails[message.conversationId!],
            },
          }));
        }
        break;
    }
  }, [onMessage]);

  // API-like functions that return promises
  const getConversations = useCallback(async (params?: {
    page?: number;
    limit?: number;
    status?: 'active' | 'inactive' | 'archived';
  }): Promise<ConversationsResponse> => {
    setState(prev => ({
      ...prev,
      conversations: { ...prev.conversations, loading: true, error: null },
    }));

    const { requestId, success } = requestConversations(params);
    
    if (!success) {
      setState(prev => ({
        ...prev,
        conversations: { ...prev.conversations, loading: false, error: 'Failed to send request' },
      }));
      throw new Error('Failed to send request');
    }

    return new Promise((resolve, reject) => {
      pendingRequestsRef.current[requestId] = {
        type: 'GET_CONVERSATIONS',
        resolve: (data) => {
          setState(prev => ({
            ...prev,
            conversations: {
              data: data?.conversations || [],
              loading: false,
              error: null,
              pagination: data?.pagination,
            },
          }));
          resolve({
            success: true,
            data: {
              conversations: data?.conversations || [],
              pagination: data?.pagination || { page: 1, limit: 50, total: 0, totalPages: 0 },
            },
            conversations: data?.conversations || [],
            pagination: data?.pagination,
          });
        },
        reject: (error) => {
          setState(prev => ({
            ...prev,
            conversations: { ...prev.conversations, loading: false, error: error.message },
          }));
          reject(error);
        },
      };

      // Set timeout for request
      setTimeout(() => {
        if (pendingRequestsRef.current[requestId]) {
          const request = pendingRequestsRef.current[requestId];
          delete pendingRequestsRef.current[requestId];
          request.reject(new Error('Request timeout'));
        }
      }, 30000);
    });
  }, [requestConversations]);

  const getConversation = useCallback(async (conversationId: string): Promise<ConversationResponse> => {
    setState(prev => ({
      ...prev,
      conversationDetails: {
        ...prev.conversationDetails,
        [conversationId]: { data: null, loading: true, error: null },
      },
    }));

    const { requestId, success } = requestConversation(conversationId);

    if (!success) {
      setState(prev => ({
        ...prev,
        conversationDetails: {
          ...prev.conversationDetails,
          [conversationId]: { data: null, loading: false, error: 'Failed to send request' },
        },
      }));
      throw new Error('Failed to send request');
    }

    return new Promise((resolve, reject) => {
      pendingRequestsRef.current[requestId] = {
        type: 'GET_CONVERSATION',
        conversationId,
        resolve: (data) => {
          setState(prev => ({
            ...prev,
            conversationDetails: {
              ...prev.conversationDetails,
              [conversationId]: {
                data: data?.conversation || null,
                loading: false,
                error: null,
              },
            },
          }));
          resolve({
            success: true,
            data: { conversation: data?.conversation },
            conversation: data?.conversation,
          });
        },
        reject: (error) => {
          setState(prev => ({
            ...prev,
            conversationDetails: {
              ...prev.conversationDetails,
              [conversationId]: { data: null, loading: false, error: error.message },
            },
          }));
          reject(error);
        },
      };

      setTimeout(() => {
        if (pendingRequestsRef.current[requestId]) {
          const request = pendingRequestsRef.current[requestId];
          delete pendingRequestsRef.current[requestId];
          request.reject(new Error('Request timeout'));
        }
      }, 30000);
    });
  }, [requestConversation]);

  const getMessages = useCallback(async (params: {
    conversationId: string;
    page?: number;
    limit?: number;
  }): Promise<MessagesResponse> => {
    const { conversationId } = params;

    setState(prev => ({
      ...prev,
      messages: {
        ...prev.messages,
        [conversationId]: {
          data: prev.messages[conversationId]?.data || [],
          loading: true,
          error: null
        },
      },
    }));

    const { requestId, success } = requestMessages(conversationId, params);

    if (!success) {
      setState(prev => ({
        ...prev,
        messages: {
          ...prev.messages,
          [conversationId]: {
            data: prev.messages[conversationId]?.data || [],
            loading: false,
            error: 'Failed to send request'
          },
        },
      }));
      throw new Error('Failed to send request');
    }

    return new Promise((resolve, reject) => {
      pendingRequestsRef.current[requestId] = {
        type: 'GET_MESSAGES',
        conversationId,
        resolve: (data) => {
          setState(prev => ({
            ...prev,
            messages: {
              ...prev.messages,
              [conversationId]: {
                data: data?.messages || [],
                loading: false,
                error: null,
                pagination: data?.pagination,
              },
            },
          }));
          resolve({
            success: true,
            data: {
              messages: data?.messages || [],
              pagination: data?.pagination || { page: 1, limit: 50, total: 0, totalPages: 0 },
            },
            messages: data?.messages || [],
            pagination: data?.pagination,
          });
        },
        reject: (error) => {
          setState(prev => ({
            ...prev,
            messages: {
              ...prev.messages,
              [conversationId]: {
                data: prev.messages[conversationId]?.data || [],
                loading: false,
                error: error.message
              },
            },
          }));
          reject(error);
        },
      };

      setTimeout(() => {
        if (pendingRequestsRef.current[requestId]) {
          const request = pendingRequestsRef.current[requestId];
          delete pendingRequestsRef.current[requestId];
          request.reject(new Error('Request timeout'));
        }
      }, 30000);
    });
  }, [requestMessages]);

  const createConversation = useCallback(async (data: {
    nurseId: string;
    nurseName: string;
    initialMessage?: string;
  }): Promise<ConversationResponse> => {
    const { requestId, success } = createConversationWS(data.nurseId, data.nurseName, data.initialMessage);

    if (!success) {
      throw new Error('Failed to send request');
    }

    return new Promise((resolve, reject) => {
      pendingRequestsRef.current[requestId] = {
        type: 'CREATE_CONVERSATION',
        resolve: (responseData) => {
          resolve({
            success: true,
            data: { conversation: responseData?.conversation },
            conversation: responseData?.conversation,
          });
        },
        reject,
      };

      setTimeout(() => {
        if (pendingRequestsRef.current[requestId]) {
          const request = pendingRequestsRef.current[requestId];
          delete pendingRequestsRef.current[requestId];
          request.reject(new Error('Request timeout'));
        }
      }, 30000);
    });
  }, [createConversationWS]);

  const markMessagesAsRead = useCallback(async (conversationId: string): Promise<{ success: boolean }> => {
    const { requestId, success } = markMessagesAsReadWS(conversationId);

    if (!success) {
      throw new Error('Failed to send request');
    }

    return new Promise((resolve, reject) => {
      pendingRequestsRef.current[requestId] = {
        type: 'MARK_AS_READ',
        conversationId,
        resolve: () => {
          resolve({ success: true });
        },
        reject,
      };

      setTimeout(() => {
        if (pendingRequestsRef.current[requestId]) {
          const request = pendingRequestsRef.current[requestId];
          delete pendingRequestsRef.current[requestId];
          request.reject(new Error('Request timeout'));
        }
      }, 30000);
    });
  }, [markMessagesAsReadWS]);

  const updateConversationStatus = useCallback(async (data: {
    conversationId: string;
    status: 'active' | 'inactive' | 'archived';
  }): Promise<ConversationResponse> => {
    const { requestId, success } = updateConversationStatusWS(data.conversationId, data.status);

    if (!success) {
      throw new Error('Failed to send request');
    }

    return new Promise((resolve, reject) => {
      pendingRequestsRef.current[requestId] = {
        type: 'UPDATE_STATUS',
        conversationId: data.conversationId,
        resolve: (responseData) => {
          resolve({
            success: true,
            data: { conversation: responseData?.conversation },
            conversation: responseData?.conversation,
          });
        },
        reject,
      };

      setTimeout(() => {
        if (pendingRequestsRef.current[requestId]) {
          const request = pendingRequestsRef.current[requestId];
          delete pendingRequestsRef.current[requestId];
          request.reject(new Error('Request timeout'));
        }
      }, 30000);
    });
  }, [updateConversationStatusWS]);

  return {
    state,
    handleWebSocketMessage,
    getConversations,
    getConversation,
    getMessages,
    createConversation,
    markMessagesAsRead,
    updateConversationStatus,
  };
};
